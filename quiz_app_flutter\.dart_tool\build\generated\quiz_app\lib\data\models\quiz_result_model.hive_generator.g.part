// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuizResultModelAdapter extends TypeAdapter<QuizResultModel> {
  @override
  final int typeId = 0;

  @override
  QuizResultModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuizResultModel(
      id: fields[0] as String,
      quizId: fields[1] as String,
      category: fields[2] as String,
      difficulty: fields[3] as String,
      totalQuestions: fields[4] as int,
      correctAnswers: fields[5] as int,
      timeSpent: fields[6] as int,
      completedAt: fields[7] as DateTime,
      userAnswers: (fields[8] as List).cast<UserAnswerModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, QuizResultModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.quizId)
      ..writeByte(2)
      ..write(obj.category)
      ..writeByte(3)
      ..write(obj.difficulty)
      ..writeByte(4)
      ..write(obj.totalQuestions)
      ..writeByte(5)
      ..write(obj.correctAnswers)
      ..writeByte(6)
      ..write(obj.timeSpent)
      ..writeByte(7)
      ..write(obj.completedAt)
      ..writeByte(8)
      ..write(obj.userAnswers);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuizResultModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserAnswerModelAdapter extends TypeAdapter<UserAnswerModel> {
  @override
  final int typeId = 1;

  @override
  UserAnswerModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserAnswerModel(
      question: fields[0] as String,
      userAnswer: fields[1] as String,
      correctAnswer: fields[2] as String,
      isCorrect: fields[3] as bool,
      timeSpent: fields[4] as int,
    );
  }

  @override
  void write(BinaryWriter writer, UserAnswerModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.question)
      ..writeByte(1)
      ..write(obj.userAnswer)
      ..writeByte(2)
      ..write(obj.correctAnswer)
      ..writeByte(3)
      ..write(obj.isCorrect)
      ..writeByte(4)
      ..write(obj.timeSpent);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAnswerModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
