// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuizResultModel _$QuizResultModelFromJson(Map<String, dynamic> json) =>
    QuizResultModel(
      id: json['id'] as String,
      quizId: json['quizId'] as String,
      category: json['category'] as String,
      difficulty: json['difficulty'] as String,
      totalQuestions: (json['totalQuestions'] as num).toInt(),
      correctAnswers: (json['correctAnswers'] as num).toInt(),
      timeSpent: (json['timeSpent'] as num).toInt(),
      completedAt: DateTime.parse(json['completedAt'] as String),
      userAnswers: (json['userAnswers'] as List<dynamic>)
          .map((e) => UserAnswerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$QuizResultModelToJson(QuizResultModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'quizId': instance.quizId,
      'category': instance.category,
      'difficulty': instance.difficulty,
      'totalQuestions': instance.totalQuestions,
      'correctAnswers': instance.correctAnswers,
      'timeSpent': instance.timeSpent,
      'completedAt': instance.completedAt.toIso8601String(),
      'userAnswers': instance.userAnswers,
    };

UserAnswerModel _$UserAnswerModelFromJson(Map<String, dynamic> json) =>
    UserAnswerModel(
      question: json['question'] as String,
      userAnswer: json['userAnswer'] as String,
      correctAnswer: json['correctAnswer'] as String,
      isCorrect: json['isCorrect'] as bool,
      timeSpent: (json['timeSpent'] as num).toInt(),
    );

Map<String, dynamic> _$UserAnswerModelToJson(UserAnswerModel instance) =>
    <String, dynamic>{
      'question': instance.question,
      'userAnswer': instance.userAnswer,
      'correctAnswer': instance.correctAnswer,
      'isCorrect': instance.isCorrect,
      'timeSpent': instance.timeSpent,
    };
