class AppConstants {
  // API Constants
  static const String baseUrl = 'https://opentdb.com/';
  static const int defaultQuestionAmount = 10;
  static const int maxQuestionAmount = 50;
  static const int minQuestionAmount = 1;

  // Quiz Settings
  static const int defaultTimePerQuestion = 30; // seconds
  static const int maxTimePerQuestion = 60; // seconds
  static const int minTimePerQuestion = 10; // seconds

  // Difficulty Levels
  static const String difficultyEasy = 'easy';
  static const String difficultyMedium = 'medium';
  static const String difficultyHard = 'hard';

  static const List<String> difficulties = [
    difficultyEasy,
    difficultyMedium,
    difficultyHard,
  ];

  // Question Types
  static const String typeMultiple = 'multiple';
  static const String typeBoolean = 'boolean';

  static const List<String> questionTypes = [
    typeMultiple,
    typeBoolean,
  ];

  // Storage Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keySoundEnabled = 'sound_enabled';
  static const String keyVibrationEnabled = 'vibration_enabled';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyQuestionTime = 'question_time';
  static const String keyFirstLaunch = 'first_launch';

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  static const double cardElevation = 2.0;
  static const double modalElevation = 8.0;

  // Score Thresholds
  static const double excellentScoreThreshold = 90.0;
  static const double goodScoreThreshold = 70.0;
  static const double averageScoreThreshold = 50.0;

  // Notification IDs
  static const int dailyReminderNotificationId = 1;
  static const int quizCompletedNotificationId = 2;
  static const int achievementNotificationId = 3;

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String quizResultsCollection = 'quiz_results';
  static const String leaderboardCollection = 'leaderboard';

  // Audio Files
  static const String correctAnswerSound = 'assets/audio/correct.mp3';
  static const String incorrectAnswerSound = 'assets/audio/incorrect.mp3';
  static const String buttonClickSound = 'assets/audio/click.mp3';
  static const String quizCompleteSound = 'assets/audio/complete.mp3';

  // Animation Files
  static const String splashAnimation = 'assets/animations/splash.json';
  static const String loadingAnimation = 'assets/animations/loading.json';
  static const String successAnimation = 'assets/animations/success.json';
  static const String errorAnimation = 'assets/animations/error.json';

  // Image Assets
  static const String appLogo = 'assets/images/logo.png';
  static const String emptyStateImage = 'assets/images/empty_state.png';
  static const String noInternetImage = 'assets/images/no_internet.png';

  // Supported Languages
  static const List<String> supportedLanguages = ['en', 'fr', 'ar'];
  static const String defaultLanguage = 'en';

  // Error Messages
  static const String networkErrorMessage = 'No internet connection';
  static const String serverErrorMessage = 'Server error occurred';
  static const String unknownErrorMessage = 'An unknown error occurred';
  static const String noQuestionsErrorMessage = 'No questions available';

  // Success Messages
  static const String quizSavedMessage = 'Quiz result saved successfully';
  static const String settingsSavedMessage = 'Settings saved successfully';
}
