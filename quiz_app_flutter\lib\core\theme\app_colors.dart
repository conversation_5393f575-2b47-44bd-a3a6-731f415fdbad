import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors (inspired by the blue from the images)
  static const Color primary = Color(0xFF6366F1);
  static const Color primaryLight = Color(0xFF8B8CF1);
  static const Color primaryDark = Color(0xFF4F46E5);

  // Secondary Colors
  static const Color secondary = Color(0xFF10B981);
  static const Color secondaryLight = Color(0xFF34D399);
  static const Color secondaryDark = Color(0xFF059669);

  // Success Colors (for correct answers)
  static const Color success = Color(0xFF10B981);
  static const Color successLight = Color(0xFFD1FAE5);
  static const Color successDark = Color(0xFF047857);

  // Error Colors (for incorrect answers)
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFFEE2E2);
  static const Color errorDark = Color(0xFFDC2626);

  // Warning Colors
  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFEF3C7);
  static const Color warningDark = Color(0xFFD97706);

  // Neutral Colors - Light Theme
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFF5F5F5);
  static const Color surfaceVariant = Color(0xFFE5E7EB);
  
  static const Color textPrimary = Color(0xFF111827);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);

  // Neutral Colors - Dark Theme
  static const Color backgroundDark = Color(0xFF0F172A);
  static const Color surfaceDark = Color(0xFF1E293B);
  static const Color surfaceVariantDark = Color(0xFF334155);
  
  static const Color textPrimaryDark = Color(0xFFF8FAFC);
  static const Color textSecondaryDark = Color(0xFFCBD5E1);
  static const Color textTertiaryDark = Color(0xFF94A3B8);

  // Category Colors (matching the images)
  static const Color categoryGeneral = Color(0xFF6366F1);
  static const Color categoryScience = Color(0xFF10B981);
  static const Color categoryComputers = Color(0xFF3B82F6);
  static const Color categorySports = Color(0xFFF59E0B);
  static const Color categoryGeography = Color(0xFFEF4444);
  static const Color categoryHistory = Color(0xFF8B5CF6);
  static const Color categoryArt = Color(0xFFEC4899);
  static const Color categoryAnimals = Color(0xFF06B6D4);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [success, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Shadow Colors
  static const Color shadow = Color(0xFF000000);
  static const Color shadowLight = Color(0x0A000000);

  // Progress Colors
  static const Color progressBackground = Color(0xFFE5E7EB);
  static const Color progressForeground = primary;

  // Quiz Specific Colors
  static const Color correctAnswer = success;
  static const Color incorrectAnswer = error;
  static const Color selectedAnswer = primary;
  static const Color unselectedAnswer = surface;

  // Chart Colors
  static const List<Color> chartColors = [
    primary,
    secondary,
    warning,
    error,
    Color(0xFF8B5CF6),
    Color(0xFFEC4899),
    Color(0xFF06B6D4),
    Color(0xFF84CC16),
  ];
}
