import 'package:hive_flutter/hive_flutter.dart';
import '../models/quiz_result_model.dart';
import '../models/question_model.dart';

class LocalStorageService {
  static const String _quizResultsBox = 'quiz_results';
  static const String _questionsBox = 'questions';
  static const String _settingsBox = 'settings';

  // Quiz Results
  Future<Box<QuizResultModel>> get _quizResultsBoxFuture async =>
      await Hive.openBox<QuizResultModel>(_quizResultsBox);

  Future<void> saveQuizResult(QuizResultModel result) async {
    final box = await _quizResultsBoxFuture;
    await box.put(result.id, result);
  }

  Future<List<QuizResultModel>> getAllQuizResults() async {
    final box = await _quizResultsBoxFuture;
    return box.values.toList();
  }

  Future<QuizResultModel?> getQuizResult(String id) async {
    final box = await _quizResultsBoxFuture;
    return box.get(id);
  }

  Future<void> deleteQuizResult(String id) async {
    final box = await _quizResultsBoxFuture;
    await box.delete(id);
  }

  Future<void> deleteAllQuizResults() async {
    final box = await _quizResultsBoxFuture;
    await box.clear();
  }

  // Questions Cache
  Future<Box<QuestionModel>> get _questionsBoxFuture async =>
      await Hive.openBox<QuestionModel>(_questionsBox);

  Future<void> cacheQuestions(List<QuestionModel> questions) async {
    final box = await _questionsBoxFuture;
    await box.clear();
    for (int i = 0; i < questions.length; i++) {
      await box.put(i, questions[i]);
    }
  }

  Future<List<QuestionModel>> getCachedQuestions() async {
    final box = await _questionsBoxFuture;
    return box.values.toList();
  }

  Future<void> clearQuestionsCache() async {
    final box = await _questionsBoxFuture;
    await box.clear();
  }

  // Settings
  Future<Box> get _settingsBoxFuture async =>
      await Hive.openBox(_settingsBox);

  Future<void> saveSetting(String key, dynamic value) async {
    final box = await _settingsBoxFuture;
    await box.put(key, value);
  }

  Future<T?> getSetting<T>(String key) async {
    final box = await _settingsBoxFuture;
    return box.get(key) as T?;
  }

  Future<void> deleteSetting(String key) async {
    final box = await _settingsBoxFuture;
    await box.delete(key);
  }

  Future<void> clearAllSettings() async {
    final box = await _settingsBoxFuture;
    await box.clear();
  }

  // Initialize Hive
  static Future<void> init() async {
    await Hive.initFlutter();
    
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(QuizResultModelAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(UserAnswerModelAdapter());
    }
  }

  // Close all boxes
  static Future<void> close() async {
    await Hive.close();
  }
}
