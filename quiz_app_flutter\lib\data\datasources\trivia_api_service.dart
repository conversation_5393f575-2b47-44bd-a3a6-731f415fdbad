import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/trivia_response_model.dart';

part 'trivia_api_service.g.dart';

@RestApi(baseUrl: 'https://opentdb.com/')
abstract class TriviaApiService {
  factory TriviaApiService(Dio dio, {String baseUrl}) = _TriviaApiService;

  @GET('/api.php')
  Future<TriviaResponseModel> getQuestions({
    @Query('amount') required int amount,
    @Query('category') int? category,
    @Query('difficulty') String? difficulty,
    @Query('type') String? type,
    @Query('encode') String encode = 'url3986',
  });

  @GET('/api_category.php')
  Future<CategoryResponseModel> getCategories();

  @GET('/api_count.php')
  Future<Map<String, dynamic>> getCategoryQuestionCount({
    @Query('category') required int category,
  });

  @GET('/api_count_global.php')
  Future<Map<String, dynamic>> getGlobalQuestionCount();
}
