import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/question.dart';

part 'question_model.g.dart';

@JsonSerializable()
class QuestionModel extends Question {
  const QuestionModel({
    required super.category,
    required super.type,
    required super.difficulty,
    required super.question,
    required super.correctAnswer,
    required super.incorrectAnswers,
  });

  factory QuestionModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionModelToJson(this);

  factory QuestionModel.fromEntity(Question question) {
    return QuestionModel(
      category: question.category,
      type: question.type,
      difficulty: question.difficulty,
      question: question.question,
      correctAnswer: question.correctAnswer,
      incorrectAnswers: question.incorrectAnswers,
    );
  }

  Question toEntity() {
    return Question(
      category: category,
      type: type,
      difficulty: difficulty,
      question: question,
      correctAnswer: correctAnswer,
      incorrectAnswers: incorrectAnswers,
    );
  }
}
