import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/quiz_result.dart';

part 'quiz_result_model.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class QuizResultModel extends QuizResult {
  @override
  @HiveField(0)
  final String id;

  @override
  @HiveField(1)
  final String quizId;

  @override
  @HiveField(2)
  final String category;

  @override
  @HiveField(3)
  final String difficulty;

  @override
  @HiveField(4)
  final int totalQuestions;

  @override
  @HiveField(5)
  final int correctAnswers;

  @override
  @HiveField(6)
  final int timeSpent;

  @override
  @HiveField(7)
  final DateTime completedAt;

  @override
  @HiveField(8)
  final List<UserAnswerModel> userAnswers;

  const QuizResultModel({
    required this.id,
    required this.quizId,
    required this.category,
    required this.difficulty,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.timeSpent,
    required this.completedAt,
    required this.userAnswers,
  }) : super(
          id: id,
          quizId: quizId,
          category: category,
          difficulty: difficulty,
          totalQuestions: totalQuestions,
          correctAnswers: correctAnswers,
          timeSpent: timeSpent,
          completedAt: completedAt,
          userAnswers: userAnswers,
        );

  factory QuizResultModel.fromJson(Map<String, dynamic> json) =>
      _$QuizResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuizResultModelToJson(this);

  factory QuizResultModel.fromEntity(QuizResult result) {
    return QuizResultModel(
      id: result.id,
      quizId: result.quizId,
      category: result.category,
      difficulty: result.difficulty,
      totalQuestions: result.totalQuestions,
      correctAnswers: result.correctAnswers,
      timeSpent: result.timeSpent,
      completedAt: result.completedAt,
      userAnswers: result.userAnswers
          .map((answer) => UserAnswerModel.fromEntity(answer))
          .toList(),
    );
  }

  QuizResult toEntity() {
    return QuizResult(
      id: id,
      quizId: quizId,
      category: category,
      difficulty: difficulty,
      totalQuestions: totalQuestions,
      correctAnswers: correctAnswers,
      timeSpent: timeSpent,
      completedAt: completedAt,
      userAnswers: userAnswers.map((answer) => answer.toEntity()).toList(),
    );
  }
}

@HiveType(typeId: 1)
@JsonSerializable()
class UserAnswerModel extends UserAnswer {
  @override
  @HiveField(0)
  final String question;

  @override
  @HiveField(1)
  final String userAnswer;

  @override
  @HiveField(2)
  final String correctAnswer;

  @override
  @HiveField(3)
  final bool isCorrect;

  @override
  @HiveField(4)
  final int timeSpent;

  const UserAnswerModel({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.timeSpent,
  }) : super(
          question: question,
          userAnswer: userAnswer,
          correctAnswer: correctAnswer,
          isCorrect: isCorrect,
          timeSpent: timeSpent,
        );

  factory UserAnswerModel.fromJson(Map<String, dynamic> json) =>
      _$UserAnswerModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserAnswerModelToJson(this);

  factory UserAnswerModel.fromEntity(UserAnswer answer) {
    return UserAnswerModel(
      question: answer.question,
      userAnswer: answer.userAnswer,
      correctAnswer: answer.correctAnswer,
      isCorrect: answer.isCorrect,
      timeSpent: answer.timeSpent,
    );
  }

  UserAnswer toEntity() {
    return UserAnswer(
      question: question,
      userAnswer: userAnswer,
      correctAnswer: correctAnswer,
      isCorrect: isCorrect,
      timeSpent: timeSpent,
    );
  }
}
