import 'package:json_annotation/json_annotation.dart';
import 'question_model.dart';

part 'trivia_response_model.g.dart';

@JsonSerializable()
class TriviaResponseModel {
  @JsonKey(name: 'response_code')
  final int responseCode;
  final List<QuestionModel> results;

  const TriviaResponseModel({
    required this.responseCode,
    required this.results,
  });

  factory TriviaResponseModel.fromJson(Map<String, dynamic> json) =>
      _$TriviaResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$TriviaResponseModelToJson(this);
}

@JsonSerializable()
class CategoryResponseModel {
  @JsonKey(name: 'trivia_categories')
  final List<CategoryModel> categories;

  const CategoryResponseModel({
    required this.categories,
  });

  factory CategoryResponseModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryResponseModelToJson(this);
}

@JsonSerializable()
class CategoryModel {
  final int id;
  final String name;

  const CategoryModel({
    required this.id,
    required this.name,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);
}
