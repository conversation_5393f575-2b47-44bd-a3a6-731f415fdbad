// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trivia_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TriviaResponseModel _$TriviaResponseModelFromJson(Map<String, dynamic> json) =>
    TriviaResponseModel(
      responseCode: (json['response_code'] as num).toInt(),
      results: (json['results'] as List<dynamic>)
          .map((e) => QuestionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TriviaResponseModelToJson(
        TriviaResponseModel instance) =>
    <String, dynamic>{
      'response_code': instance.responseCode,
      'results': instance.results,
    };

CategoryResponseModel _$CategoryResponseModelFromJson(
        Map<String, dynamic> json) =>
    CategoryResponseModel(
      categories: (json['trivia_categories'] as List<dynamic>)
          .map((e) => CategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CategoryResponseModelToJson(
        CategoryResponseModel instance) =>
    <String, dynamic>{
      'trivia_categories': instance.categories,
    };

CategoryModel _$CategoryModelFromJson(Map<String, dynamic> json) =>
    CategoryModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$CategoryModelToJson(CategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };
