import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_category.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../datasources/trivia_api_service.dart';
import '../datasources/local_storage_service.dart';
import '../models/question_model.dart';

class QuizRepositoryImpl implements QuizRepository {
  final TriviaApiService _apiService;
  final LocalStorageService _localStorageService;

  QuizRepositoryImpl(this._apiService, this._localStorageService);

  @override
  Future<List<Question>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    try {
      final response = await _apiService.getQuestions(
        amount: amount,
        category: category,
        difficulty: difficulty,
        type: type,
      );

      if (response.responseCode == 0) {
        final questions = response.results.map((model) => model.toEntity()).toList();
        
        // Cache questions for offline use
        await cacheQuestions(questions);
        
        return questions;
      } else {
        throw Exception('Failed to fetch questions: Response code ${response.responseCode}');
      }
    } catch (e) {
      // If API fails, try to get cached questions
      final cachedQuestions = await getCachedQuestions();
      if (cachedQuestions.isNotEmpty) {
        return cachedQuestions;
      }
      rethrow;
    }
  }

  @override
  Future<List<QuizCategory>> getCategories() async {
    try {
      final response = await _apiService.getCategories();
      
      // Map API categories to our predefined categories with icons and colors
      final apiCategories = response.categories;
      final mappedCategories = <QuizCategory>[];
      
      for (final predefinedCategory in QuizCategories.categories) {
        final apiCategory = apiCategories.firstWhere(
          (cat) => cat.id == predefinedCategory.id,
          orElse: () => throw Exception('Category not found'),
        );
        
        mappedCategories.add(predefinedCategory);
            }
      
      return mappedCategories;
    } catch (e) {
      // Return predefined categories if API fails
      return QuizCategories.categories;
    }
  }

  @override
  Future<void> cacheQuestions(List<Question> questions) async {
    final questionModels = questions
        .map((question) => QuestionModel.fromEntity(question))
        .toList();
    await _localStorageService.cacheQuestions(questionModels);
  }

  @override
  Future<List<Question>> getCachedQuestions() async {
    final cachedModels = await _localStorageService.getCachedQuestions();
    return cachedModels.map((model) => model.toEntity()).toList();
  }

  @override
  Future<void> clearCache() async {
    await _localStorageService.clearQuestionsCache();
  }
}
