import '../../domain/entities/quiz_result.dart';
import '../../domain/repositories/quiz_result_repository.dart';
import '../datasources/local_storage_service.dart';
import '../models/quiz_result_model.dart';

class QuizResultRepositoryImpl implements QuizResultRepository {
  final LocalStorageService _localStorageService;

  QuizResultRepositoryImpl(this._localStorageService);

  @override
  Future<void> saveQuizResult(QuizResult result) async {
    final resultModel = QuizResultModel.fromEntity(result);
    await _localStorageService.saveQuizResult(resultModel);
  }

  @override
  Future<List<QuizResult>> getAllResults() async {
    final resultModels = await _localStorageService.getAllQuizResults();
    return resultModels.map((model) => model.toEntity()).toList();
  }

  @override
  Future<List<QuizResult>> getResultsByCategory(String category) async {
    final allResults = await getAllResults();
    return allResults.where((result) => result.category == category).toList();
  }

  @override
  Future<List<QuizResult>> getResultsByDifficulty(String difficulty) async {
    final allResults = await getAllResults();
    return allResults.where((result) => result.difficulty == difficulty).toList();
  }

  @override
  Future<QuizResult?> getBestResult() async {
    final allResults = await getAllResults();
    if (allResults.isEmpty) return null;

    allResults.sort((a, b) => b.scorePercentage.compareTo(a.scorePercentage));
    return allResults.first;
  }

  @override
  Future<QuizResult?> getLatestResult() async {
    final allResults = await getAllResults();
    if (allResults.isEmpty) return null;

    allResults.sort((a, b) => b.completedAt.compareTo(a.completedAt));
    return allResults.first;
  }

  @override
  Future<void> deleteResult(String id) async {
    await _localStorageService.deleteQuizResult(id);
  }

  @override
  Future<void> deleteAllResults() async {
    await _localStorageService.deleteAllQuizResults();
  }

  @override
  Future<double> getAverageScore() async {
    final allResults = await getAllResults();
    if (allResults.isEmpty) return 0.0;

    final totalScore = allResults.fold<double>(
      0.0,
      (sum, result) => sum + result.scorePercentage,
    );

    return totalScore / allResults.length;
  }

  @override
  Future<int> getTotalQuizzesCompleted() async {
    final allResults = await getAllResults();
    return allResults.length;
  }
}
