import 'package:equatable/equatable.dart';
import 'question.dart';

class Quiz extends Equatable {
  final String id;
  final String category;
  final String difficulty;
  final List<Question> questions;
  final DateTime createdAt;

  const Quiz({
    required this.id,
    required this.category,
    required this.difficulty,
    required this.questions,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
        id,
        category,
        difficulty,
        questions,
        createdAt,
      ];
}
