import 'package:equatable/equatable.dart';

class QuizCategory extends Equatable {
  final int id;
  final String name;
  final String iconPath;
  final String color;

  const QuizCategory({
    required this.id,
    required this.name,
    required this.iconPath,
    required this.color,
  });

  @override
  List<Object?> get props => [id, name, iconPath, color];
}

// Predefined categories with their Open Trivia DB IDs
class QuizCategories {
  static const List<QuizCategory> categories = [
    QuizCategory(
      id: 9,
      name: 'General Knowledge',
      iconPath: 'assets/images/general.png',
      color: '#6366F1',
    ),
    QuizCategory(
      id: 17,
      name: 'Science & Nature',
      iconPath: 'assets/images/science.png',
      color: '#10B981',
    ),
    QuizCategory(
      id: 18,
      name: 'Computers',
      iconPath: 'assets/images/computers.png',
      color: '#3B82F6',
    ),
    QuizCategory(
      id: 21,
      name: 'Sports',
      iconPath: 'assets/images/sports.png',
      color: '#F59E0B',
    ),
    QuizCategory(
      id: 22,
      name: 'Geography',
      iconPath: 'assets/images/geography.png',
      color: '#EF4444',
    ),
    QuizCategory(
      id: 23,
      name: 'History',
      iconPath: 'assets/images/history.png',
      color: '#8B5CF6',
    ),
    QuizCategory(
      id: 25,
      name: 'Art',
      iconPath: 'assets/images/art.png',
      color: '#EC4899',
    ),
    QuizCategory(
      id: 27,
      name: 'Animals',
      iconPath: 'assets/images/animals.png',
      color: '#06B6D4',
    ),
  ];
}
