import 'package:equatable/equatable.dart';

class QuizResult extends Equatable {
  final String id;
  final String quizId;
  final String category;
  final String difficulty;
  final int totalQuestions;
  final int correctAnswers;
  final int timeSpent; // in seconds
  final DateTime completedAt;
  final List<UserAnswer> userAnswers;

  const QuizResult({
    required this.id,
    required this.quizId,
    required this.category,
    required this.difficulty,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.timeSpent,
    required this.completedAt,
    required this.userAnswers,
  });

  double get scorePercentage => (correctAnswers / totalQuestions) * 100;

  int get incorrectAnswers => totalQuestions - correctAnswers;

  @override
  List<Object?> get props => [
        id,
        quizId,
        category,
        difficulty,
        totalQuestions,
        correctAnswers,
        timeSpent,
        completedAt,
        userAnswers,
      ];
}

class UserAnswer extends Equatable {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;
  final int timeSpent; // in seconds

  const User<PERSON>ns<PERSON>({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.timeSpent,
  });

  @override
  List<Object?> get props => [
        question,
        userAnswer,
        correctAnswer,
        isCorrect,
        timeSpent,
      ];
}
