import '../entities/question.dart';
import '../entities/quiz_category.dart';

abstract class QuizRepository {
  Future<List<Question>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  });

  Future<List<QuizCategory>> getCategories();

  Future<void> cacheQuestions(List<Question> questions);

  Future<List<Question>> getCachedQuestions();

  Future<void> clearCache();
}
