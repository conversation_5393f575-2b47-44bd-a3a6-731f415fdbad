import '../entities/quiz_result.dart';

abstract class QuizResultRepository {
  Future<void> saveQuizResult(QuizResult result);

  Future<List<QuizResult>> getAllResults();

  Future<List<QuizResult>> getResultsByCategory(String category);

  Future<List<QuizResult>> getResultsByDifficulty(String difficulty);

  Future<QuizResult?> getBestResult();

  Future<QuizResult?> getLatestResult();

  Future<void> deleteResult(String id);

  Future<void> deleteAllResults();

  Future<double> getAverageScore();

  Future<int> getTotalQuizzesCompleted();
}
