import '../entities/question.dart';
import '../repositories/quiz_repository.dart';

class GetQuizQuestions {
  final QuizRepository _repository;

  GetQuizQuestions(this._repository);

  Future<List<Question>> call({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    return await _repository.getQuestions(
      amount: amount,
      category: category,
      difficulty: difficulty,
      type: type,
    );
  }
}
