import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_result.dart';
import '../providers/quiz_providers.dart';
import '../providers/settings_providers.dart';
import 'quiz_result_page.dart';

class QuizPage extends ConsumerStatefulWidget {
  final String categoryName;
  final String difficulty;
  final int questionCount;

  const QuizPage({
    super.key,
    required this.categoryName,
    required this.difficulty,
    required this.questionCount,
  });

  @override
  ConsumerState<QuizPage> createState() => _QuizPageState();
}

class _QuizPageState extends ConsumerState<QuizPage>
    with TickerProviderStateMixin {
  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  bool _isAnswered = false;
  List<UserAnswer> _userAnswers = [];
  Timer? _timer;
  int _timeLeft = AppConstants.defaultTimePerQuestion;
  late AnimationController _progressController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(seconds: AppConstants.defaultTimePerQuestion),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);

    _fadeController.forward();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _progressController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startTimer() {
    final questionTime = ref.read(questionTimeProvider);
    _timeLeft = questionTime;
    _progressController.duration = Duration(seconds: questionTime);
    _progressController.forward();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft > 0 && !_isAnswered) {
        setState(() {
          _timeLeft--;
        });
      } else {
        timer.cancel();
        if (!_isAnswered) {
          _handleTimeUp();
        }
      }
    });
  }

  void _handleTimeUp() {
    if (!_isAnswered) {
      _selectAnswer(''); // Empty answer for timeout
    }
  }

  void _selectAnswer(String answer) {
    if (_isAnswered) return;

    setState(() {
      _selectedAnswer = answer;
      _isAnswered = true;
    });

    _timer?.cancel();
    _progressController.stop();

    // Add vibration and sound feedback here
    final vibrationEnabled = ref.read(vibrationEnabledProvider);
    final soundEnabled = ref.read(soundEnabledProvider);

    // TODO: Implement vibration and sound

    // Wait a moment to show the result, then move to next question
    Future.delayed(const Duration(milliseconds: 1500), () {
      _nextQuestion();
    });
  }

  void _nextQuestion() {
    final questionsAsync = ref.read(questionsProvider);
    questionsAsync.whenData((questions) {
      if (questions.isNotEmpty) {
        final currentQuestion = questions[_currentQuestionIndex];
        final isCorrect = _selectedAnswer == currentQuestion.correctAnswer;

        _userAnswers.add(UserAnswer(
          question: currentQuestion.question,
          userAnswer: _selectedAnswer ?? '',
          correctAnswer: currentQuestion.correctAnswer,
          isCorrect: isCorrect,
          timeSpent: ref.read(questionTimeProvider) - _timeLeft,
        ));

        if (_currentQuestionIndex < questions.length - 1) {
          setState(() {
            _currentQuestionIndex++;
            _selectedAnswer = null;
            _isAnswered = false;
          });

          _progressController.reset();
          _fadeController.reset();
          _fadeController.forward();
          _startTimer();
        } else {
          _finishQuiz();
        }
      }
    });
  }

  void _finishQuiz() {
    final correctAnswers = _userAnswers.where((answer) => answer.isCorrect).length;
    final totalTime = _userAnswers.fold<int>(0, (sum, answer) => sum + answer.timeSpent);

    final result = QuizResult(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      quizId: DateTime.now().millisecondsSinceEpoch.toString(),
      category: widget.categoryName,
      difficulty: widget.difficulty,
      totalQuestions: widget.questionCount,
      correctAnswers: correctAnswers,
      timeSpent: totalTime,
      completedAt: DateTime.now(),
      userAnswers: _userAnswers,
    );

    // Save result
    ref.read(quizResultsProvider.notifier).saveResult(result);

    // Navigate to results page
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => QuizResultPage(result: result),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final questionsAsync = ref.watch(questionsProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _showExitDialog(),
        ),
        title: questionsAsync.when(
          data: (questions) => Text(
            '${_currentQuestionIndex + 1} of ${questions.length}',
            style: const TextStyle(fontSize: 16),
          ),
          loading: () => const Text('Loading...'),
          error: (_, __) => const Text('Error'),
        ),
        centerTitle: true,
      ),
      body: questionsAsync.when(
        data: (questions) => questions.isEmpty
            ? _buildEmptyState()
            : _buildQuizContent(questions[_currentQuestionIndex]),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildQuizContent(Question question) {
    final progress = (_currentQuestionIndex + 1) / widget.questionCount;

    return Column(
      children: [
        // Progress bar
        Container(
          height: 4,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: AppColors.surface,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),

        // Timer
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.timer_outlined,
                color: _timeLeft <= 5 ? AppColors.error : AppColors.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '$_timeLeft',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: _timeLeft <= 5 ? AppColors.error : AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),

        Expanded(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question
                  Text(
                    question.question,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                      height: 1.3,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Answers
                  Expanded(
                    child: ListView.builder(
                      itemCount: question.allAnswers.length,
                      itemBuilder: (context, index) {
                        final answer = question.allAnswers[index];
                        return _buildAnswerOption(answer, question.correctAnswer);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnswerOption(String answer, String correctAnswer) {
    Color? backgroundColor;
    Color? borderColor;
    Color? textColor;
    IconData? icon;

    if (_isAnswered) {
      if (answer == correctAnswer) {
        backgroundColor = AppColors.success.withOpacity(0.1);
        borderColor = AppColors.success;
        textColor = AppColors.success;
        icon = Icons.check_circle;
      } else if (answer == _selectedAnswer) {
        backgroundColor = AppColors.error.withOpacity(0.1);
        borderColor = AppColors.error;
        textColor = AppColors.error;
        icon = Icons.cancel;
      } else {
        backgroundColor = AppColors.surface;
        borderColor = AppColors.surfaceVariant;
        textColor = AppColors.textSecondary;
      }
    } else if (answer == _selectedAnswer) {
      backgroundColor = AppColors.primary.withOpacity(0.1);
      borderColor = AppColors.primary;
      textColor = AppColors.primary;
    } else {
      backgroundColor = AppColors.surface;
      borderColor = AppColors.surfaceVariant;
      textColor = AppColors.textPrimary;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _selectAnswer(answer),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderColor!, width: 2),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  answer,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: textColor,
                  ),
                ),
              ),
              if (icon != null) ...[
                const SizedBox(width: 12),
                Icon(icon, color: borderColor, size: 24),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading questions...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.quiz_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16),
          Text(
            'No questions available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Please try again later',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load questions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Quiz?'),
        content: const Text('Your progress will be lost if you exit now.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Exit quiz
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }
}
