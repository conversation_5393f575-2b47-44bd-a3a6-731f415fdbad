import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../domain/entities/quiz_result.dart';
import '../../shared/widgets/circular_progress_widget.dart';
import 'home_page.dart';

class QuizResultPage extends ConsumerStatefulWidget {
  final QuizResult result;

  const QuizResultPage({
    super.key,
    required this.result,
  });

  @override
  ConsumerState<QuizResultPage> createState() => _QuizResultPageState();
}

class _QuizResultPageState extends ConsumerState<QuizResultPage>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late Animation<Color?> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    final scorePercentage = widget.result.scorePercentage;
    Color targetColor;
    
    if (scorePercentage >= 90) {
      targetColor = AppColors.success.withOpacity(0.1);
    } else if (scorePercentage >= 70) {
      targetColor = AppColors.warning.withOpacity(0.1);
    } else {
      targetColor = AppColors.error.withOpacity(0.1);
    }

    _backgroundAnimation = ColorTween(
      begin: Colors.transparent,
      end: targetColor,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));

    // Start background animation after a delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      _backgroundController.forward();
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              color: _backgroundAnimation.value ?? Colors.transparent,
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    _buildHeader(),
                    Expanded(
                      child: _buildResultContent(),
                    ),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _navigateToHome(),
        ),
        const Spacer(),
      ],
    );
  }

  Widget _buildResultContent() {
    final percentage = widget.result.scorePercentage.round();
    final xpGained = _calculateXP();
    final message = _getResultMessage(percentage);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AnimatedScoreWidget(
          score: widget.result.correctAnswers,
          totalQuestions: widget.result.totalQuestions,
          message: message,
          xpGained: '+$xpGained XP',
        ),
        const SizedBox(height: 40),
        _buildStatsCard(),
      ],
    );
  }

  Widget _buildStatsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildStatRow(
              'Category',
              widget.result.category,
              Icons.category_outlined,
            ),
            const Divider(height: 24),
            _buildStatRow(
              'Difficulty',
              widget.result.difficulty.toUpperCase(),
              Icons.trending_up_outlined,
            ),
            const Divider(height: 24),
            _buildStatRow(
              'Time Spent',
              _formatTime(widget.result.timeSpent),
              Icons.timer_outlined,
            ),
            const Divider(height: 24),
            _buildStatRow(
              'Accuracy',
              '${widget.result.scorePercentage.round()}%',
              Icons.target_outlined,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
        const SizedBox(width: 12),
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _retryQuiz,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'TRY AGAIN',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _shareResult,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.share_outlined, size: 20),
                SizedBox(width: 8),
                Text(
                  'SHARE RESULT',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _navigateToHome,
            child: const Text(
              'BACK TO HOME',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getResultMessage(int percentage) {
    if (percentage == 100) {
      return 'You are awesome!';
    } else if (percentage >= 90) {
      return 'Excellent!';
    } else if (percentage >= 70) {
      return 'Great job!';
    } else if (percentage >= 50) {
      return 'Good effort!';
    } else {
      return 'Keep trying!';
    }
  }

  int _calculateXP() {
    final baseXP = widget.result.correctAnswers * 10;
    final difficultyMultiplier = _getDifficultyMultiplier();
    final timeBonus = _getTimeBonus();
    
    return (baseXP * difficultyMultiplier + timeBonus).round();
  }

  double _getDifficultyMultiplier() {
    switch (widget.result.difficulty.toLowerCase()) {
      case 'easy':
        return 1.0;
      case 'medium':
        return 1.5;
      case 'hard':
        return 2.0;
      default:
        return 1.0;
    }
  }

  int _getTimeBonus() {
    // Bonus points for completing quickly
    final averageTimePerQuestion = widget.result.timeSpent / widget.result.totalQuestions;
    if (averageTimePerQuestion < 10) {
      return 50;
    } else if (averageTimePerQuestion < 20) {
      return 25;
    }
    return 0;
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}m ${remainingSeconds}s';
  }

  void _retryQuiz() {
    Navigator.pop(context); // Go back to quiz selection
  }

  void _shareResult() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
      ),
    );
  }

  void _navigateToHome() {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const HomePage()),
      (route) => false,
    );
  }
}
