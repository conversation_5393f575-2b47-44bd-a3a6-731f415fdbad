import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../providers/quiz_providers.dart';
import '../providers/settings_providers.dart';
import 'quiz_page.dart';

class QuizSelectionPage extends ConsumerStatefulWidget {
  final int categoryId;
  final String categoryName;

  const QuizSelectionPage({
    super.key,
    required this.categoryId,
    required this.categoryName,
  });

  @override
  ConsumerState<QuizSelectionPage> createState() => _QuizSelectionPageState();
}

class _QuizSelectionPageState extends ConsumerState<QuizSelectionPage> {
  int _selectedQuestionCount = AppConstants.defaultQuestionAmount;
  String _selectedDifficulty = AppConstants.difficultyMedium;
  String _selectedType = AppConstants.typeMultiple;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryName),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 32),
              _buildQuestionCountSection(),
              const SizedBox(height: 24),
              _buildDifficultySection(),
              const SizedBox(height: 24),
              _buildQuestionTypeSection(),
              const Spacer(),
              _buildStartButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quiz Setup',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Customize your quiz experience by selecting the number of questions, difficulty level, and question type.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionCountSection() {
    return _buildSection(
      title: 'Number of Questions',
      child: Wrap(
        spacing: 12,
        children: [5, 10, 15, 20].map((count) {
          return _buildSelectionChip(
            label: '$count',
            isSelected: _selectedQuestionCount == count,
            onTap: () => setState(() => _selectedQuestionCount = count),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDifficultySection() {
    return _buildSection(
      title: 'Difficulty Level',
      child: Column(
        children: AppConstants.difficulties.map((difficulty) {
          return _buildDifficultyTile(difficulty);
        }).toList(),
      ),
    );
  }

  Widget _buildQuestionTypeSection() {
    return _buildSection(
      title: 'Question Type',
      child: Column(
        children: [
          _buildQuestionTypeTile(
            AppConstants.typeMultiple,
            'Multiple Choice',
            'Choose from 4 options',
            Icons.radio_button_checked,
          ),
          const SizedBox(height: 12),
          _buildQuestionTypeTile(
            AppConstants.typeBoolean,
            'True/False',
            'Simple yes or no questions',
            Icons.check_box,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }

  Widget _buildSelectionChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surface,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultyTile(String difficulty) {
    final isSelected = _selectedDifficulty == difficulty;
    final color = _getDifficultyColor(difficulty);
    
    return GestureDetector(
      onTap: () => setState(() => _selectedDifficulty = difficulty),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : AppColors.surfaceVariant,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              difficulty.toUpperCase(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isSelected ? color : AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: color,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionTypeTile(
    String type,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = _selectedType == type;
    
    return GestureDetector(
      onTap: () => setState(() => _selectedType = type),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _startQuiz,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'START QUIZ',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(width: 8),
            Icon(Icons.arrow_forward, size: 20),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case AppConstants.difficultyEasy:
        return AppColors.success;
      case AppConstants.difficultyMedium:
        return AppColors.warning;
      case AppConstants.difficultyHard:
        return AppColors.error;
      default:
        return AppColors.primary;
    }
  }

  void _startQuiz() {
    // Fetch questions and navigate to quiz page
    ref.read(questionsProvider.notifier).fetchQuestions(
      amount: _selectedQuestionCount,
      category: widget.categoryId,
      difficulty: _selectedDifficulty,
      type: _selectedType,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuizPage(
          categoryName: widget.categoryName,
          difficulty: _selectedDifficulty,
          questionCount: _selectedQuestionCount,
        ),
      ),
    );
  }
}
