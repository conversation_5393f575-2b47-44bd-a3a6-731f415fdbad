import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../../data/datasources/trivia_api_service.dart';
import '../../data/datasources/local_storage_service.dart';
import '../../data/repositories/quiz_repository_impl.dart';
import '../../data/repositories/quiz_result_repository_impl.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../../domain/repositories/quiz_result_repository.dart';
import '../../domain/usecases/get_quiz_questions.dart';
import '../../domain/usecases/get_quiz_categories.dart';
import '../../domain/usecases/save_quiz_result.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_category.dart';
import '../../domain/entities/quiz_result.dart';

// Infrastructure Providers
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio();
  dio.options.connectTimeout = const Duration(seconds: 10);
  dio.options.receiveTimeout = const Duration(seconds: 10);
  return dio;
});

final localStorageServiceProvider = Provider<LocalStorageService>((ref) {
  return LocalStorageService();
});

final triviaApiServiceProvider = Provider<TriviaApiService>((ref) {
  final dio = ref.read(dioProvider);
  return TriviaApiService(dio);
});

// Repository Providers
final quizRepositoryProvider = Provider<QuizRepository>((ref) {
  final apiService = ref.read(triviaApiServiceProvider);
  final localStorageService = ref.read(localStorageServiceProvider);
  return QuizRepositoryImpl(apiService, localStorageService);
});

final quizResultRepositoryProvider = Provider<QuizResultRepository>((ref) {
  final localStorageService = ref.read(localStorageServiceProvider);
  return QuizResultRepositoryImpl(localStorageService);
});

// Use Case Providers
final getQuizQuestionsProvider = Provider<GetQuizQuestions>((ref) {
  final repository = ref.read(quizRepositoryProvider);
  return GetQuizQuestions(repository);
});

final getQuizCategoriesProvider = Provider<GetQuizCategories>((ref) {
  final repository = ref.read(quizRepositoryProvider);
  return GetQuizCategories(repository);
});

final saveQuizResultProvider = Provider<SaveQuizResult>((ref) {
  final repository = ref.read(quizResultRepositoryProvider);
  return SaveQuizResult(repository);
});

// State Providers
final questionsProvider = StateNotifierProvider<QuestionsNotifier, AsyncValue<List<Question>>>((ref) {
  final getQuizQuestions = ref.read(getQuizQuestionsProvider);
  return QuestionsNotifier(getQuizQuestions);
});

final categoriesProvider = StateNotifierProvider<CategoriesNotifier, AsyncValue<List<QuizCategory>>>((ref) {
  final getQuizCategories = ref.read(getQuizCategoriesProvider);
  return CategoriesNotifier(getQuizCategories);
});

final quizResultsProvider = StateNotifierProvider<QuizResultsNotifier, AsyncValue<List<QuizResult>>>((ref) {
  final repository = ref.read(quizResultRepositoryProvider);
  return QuizResultsNotifier(repository);
});

// State Notifiers
class QuestionsNotifier extends StateNotifier<AsyncValue<List<Question>>> {
  final GetQuizQuestions _getQuizQuestions;

  QuestionsNotifier(this._getQuizQuestions) : super(const AsyncValue.loading());

  Future<void> fetchQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    state = const AsyncValue.loading();
    try {
      final questions = await _getQuizQuestions(
        amount: amount,
        category: category,
        difficulty: difficulty,
        type: type,
      );
      state = AsyncValue.data(questions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

class CategoriesNotifier extends StateNotifier<AsyncValue<List<QuizCategory>>> {
  final GetQuizCategories _getQuizCategories;

  CategoriesNotifier(this._getQuizCategories) : super(const AsyncValue.loading()) {
    fetchCategories();
  }

  Future<void> fetchCategories() async {
    state = const AsyncValue.loading();
    try {
      final categories = await _getQuizCategories();
      state = AsyncValue.data(categories);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

class QuizResultsNotifier extends StateNotifier<AsyncValue<List<QuizResult>>> {
  final QuizResultRepository _repository;

  QuizResultsNotifier(this._repository) : super(const AsyncValue.loading()) {
    fetchResults();
  }

  Future<void> fetchResults() async {
    state = const AsyncValue.loading();
    try {
      final results = await _repository.getAllResults();
      state = AsyncValue.data(results);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> saveResult(QuizResult result) async {
    try {
      await _repository.saveQuizResult(result);
      await fetchResults(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteResult(String id) async {
    try {
      await _repository.deleteResult(id);
      await fetchResults(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAllResults() async {
    try {
      await _repository.deleteAllResults();
      state = const AsyncValue.data([]);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
