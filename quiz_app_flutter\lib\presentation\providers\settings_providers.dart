import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/local_storage_service.dart';
import '../../core/constants/app_constants.dart';

// Settings State
class SettingsState {
  final ThemeMode themeMode;
  final String language;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool notificationsEnabled;
  final int questionTime;

  const SettingsState({
    this.themeMode = ThemeMode.system,
    this.language = AppConstants.defaultLanguage,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.notificationsEnabled = true,
    this.questionTime = AppConstants.defaultTimePerQuestion,
  });

  SettingsState copyWith({
    ThemeMode? themeMode,
    String? language,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
    int? questionTime,
  }) {
    return SettingsState(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      questionTime: questionTime ?? this.questionTime,
    );
  }
}

// Settings Notifier
class SettingsNotifier extends StateNotifier<SettingsState> {
  final LocalStorageService _localStorageService;

  SettingsNotifier(this._localStorageService) : super(const SettingsState()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final themeMode = await _getThemeMode();
      final language = await _localStorageService.getSetting<String>(AppConstants.keyLanguage) ?? AppConstants.defaultLanguage;
      final soundEnabled = await _localStorageService.getSetting<bool>(AppConstants.keySoundEnabled) ?? true;
      final vibrationEnabled = await _localStorageService.getSetting<bool>(AppConstants.keyVibrationEnabled) ?? true;
      final notificationsEnabled = await _localStorageService.getSetting<bool>(AppConstants.keyNotificationsEnabled) ?? true;
      final questionTime = await _localStorageService.getSetting<int>(AppConstants.keyQuestionTime) ?? AppConstants.defaultTimePerQuestion;

      state = SettingsState(
        themeMode: themeMode,
        language: language,
        soundEnabled: soundEnabled,
        vibrationEnabled: vibrationEnabled,
        notificationsEnabled: notificationsEnabled,
        questionTime: questionTime,
      );
    } catch (e) {
      // Use default settings if loading fails
      state = const SettingsState();
    }
  }

  Future<ThemeMode> _getThemeMode() async {
    final themeModeString = await _localStorageService.getSetting<String>(AppConstants.keyThemeMode);
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      default:
        return ThemeMode.system;
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    String themeModeString;
    switch (themeMode) {
      case ThemeMode.light:
        themeModeString = 'light';
        break;
      case ThemeMode.dark:
        themeModeString = 'dark';
        break;
      case ThemeMode.system:
        themeModeString = 'system';
        break;
    }
    
    await _localStorageService.saveSetting(AppConstants.keyThemeMode, themeModeString);
    state = state.copyWith(themeMode: themeMode);
  }

  Future<void> setLanguage(String language) async {
    await _localStorageService.saveSetting(AppConstants.keyLanguage, language);
    state = state.copyWith(language: language);
  }

  Future<void> setSoundEnabled(bool enabled) async {
    await _localStorageService.saveSetting(AppConstants.keySoundEnabled, enabled);
    state = state.copyWith(soundEnabled: enabled);
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    await _localStorageService.saveSetting(AppConstants.keyVibrationEnabled, enabled);
    state = state.copyWith(vibrationEnabled: enabled);
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    await _localStorageService.saveSetting(AppConstants.keyNotificationsEnabled, enabled);
    state = state.copyWith(notificationsEnabled: enabled);
  }

  Future<void> setQuestionTime(int time) async {
    await _localStorageService.saveSetting(AppConstants.keyQuestionTime, time);
    state = state.copyWith(questionTime: time);
  }

  Future<void> resetSettings() async {
    await _localStorageService.clearAllSettings();
    state = const SettingsState();
  }
}

// Provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, SettingsState>((ref) {
  final localStorageService = ref.read(localStorageServiceProvider);
  return SettingsNotifier(localStorageService);
});

// Convenience providers
final themeModeProvider = Provider<ThemeMode>((ref) {
  return ref.watch(settingsProvider).themeMode;
});

final languageProvider = Provider<String>((ref) {
  return ref.watch(settingsProvider).language;
});

final soundEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).soundEnabled;
});

final vibrationEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).vibrationEnabled;
});

final notificationsEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).notificationsEnabled;
});

final questionTimeProvider = Provider<int>((ref) {
  return ref.watch(settingsProvider).questionTime;
});

// Local Storage Service Provider (imported from quiz_providers.dart)
final localStorageServiceProvider = Provider<LocalStorageService>((ref) {
  return LocalStorageService();
});
