name: quiz_app
description: "Advanced Flutter Quiz App with MVVM Architecture"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # HTTP & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Firebase
  firebase_core: ^2.31.0
  firebase_messaging: ^14.9.2
  firebase_analytics: ^10.10.5

  # Notifications
  flutter_local_notifications: ^17.1.2

  # Internationalization
  easy_localization: ^3.0.7
  intl: ^0.19.0

  # UI & Animations
  animations: ^2.0.11
  liquid_progress_indicator: ^0.4.0
  lottie: ^3.1.2
  smooth_page_indicator: ^1.1.0
  flutter_staggered_animations: ^1.1.1

  # Audio & Vibration
  just_audio: ^0.9.38
  vibration: ^1.8.4

  # Charts & Graphs
  fl_chart: ^0.68.0

  # Utils
  equatable: ^2.0.5
  freezed_annotation: ^2.4.1
  go_router: ^14.2.0
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3

  # UI Components
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.9
  riverpod_generator: ^2.4.0
  json_serializable: ^6.8.0
  retrofit_generator: ^8.1.0
  freezed: ^2.5.2
  hive_generator: ^2.0.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/animations/
    - assets/audio/
    - assets/translations/

  # Fonts
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
